version: '3.8'

services:
  # API Gateway
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - AUTH_SERVICE_URL=http://auth-service:3001
      - ARCHIVE_SERVICE_URL=http://archive-service:3002
      - ASSESSMENT_SERVICE_URL=http://assessment-service:3003
      - JWT_SECRET=${JWT_SECRET:-atma_jwt_secret_change_in_production}
      - INTERNAL_SERVICE_KEY=${INTERNAL_SERVICE_KEY:-atma_internal_service_key}
      - RATE_LIMIT_WINDOW_MS=900000
      - RATE_LIMIT_MAX_REQUESTS=100
      - ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
    depends_on:
      - auth-service
      - archive-service
      - assessment-service
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/health/live', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - atma-network

  # Auth Service
  auth-service:
    build:
      context: ./auth-service
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - DB_HOST=${DB_HOST:-localhost}
      - DB_PORT=${DB_PORT:-5432}
      - DB_NAME=${DB_NAME:-atma_auth}
      - DB_USER=${DB_USER:-postgres}
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - JWT_SECRET=${JWT_SECRET:-atma_jwt_secret_change_in_production}
      - INTERNAL_SERVICE_KEY=${INTERNAL_SERVICE_KEY:-atma_internal_service_key}
    restart: unless-stopped
    networks:
      - atma-network

  # Archive Service
  archive-service:
    build:
      context: ./archive-service
      dockerfile: Dockerfile
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
      - PORT=3002
      - DB_HOST=${DB_HOST:-localhost}
      - DB_PORT=${DB_PORT:-5432}
      - DB_NAME=${DB_NAME:-atma_archive}
      - DB_USER=${DB_USER:-postgres}
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - INTERNAL_SERVICE_KEY=${INTERNAL_SERVICE_KEY:-atma_internal_service_key}
      - AUTH_SERVICE_URL=http://auth-service:3001
    restart: unless-stopped
    networks:
      - atma-network

  # Assessment Service
  assessment-service:
    build:
      context: ./assessment-service
      dockerfile: Dockerfile
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=production
      - PORT=3003
      - RABBITMQ_URL=${RABBITMQ_URL:-amqp://localhost:5672}
      - AUTH_SERVICE_URL=http://auth-service:3001
      - ARCHIVE_SERVICE_URL=http://archive-service:3002
      - INTERNAL_SERVICE_KEY=${INTERNAL_SERVICE_KEY:-atma_internal_service_key}
    restart: unless-stopped
    networks:
      - atma-network

  # RabbitMQ for Assessment Service
  rabbitmq:
    image: rabbitmq:3-management-alpine
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER:-admin}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASS:-admin123}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    restart: unless-stopped
    networks:
      - atma-network

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=${DB_USER:-postgres}
      - POSTGRES_PASSWORD=${DB_PASSWORD:-password}
      - POSTGRES_DB=${DB_NAME:-atma}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    restart: unless-stopped
    networks:
      - atma-network

volumes:
  postgres_data:
  rabbitmq_data:

networks:
  atma-network:
    driver: bridge
