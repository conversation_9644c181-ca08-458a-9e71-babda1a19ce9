# ATMA API Gateway

API Gateway untuk sistem ATMA (AI-Driven Talent Mapping Assessment) yang menyediakan endpoint terpusat untuk semua microservices.

## 🚀 Features

- **Unified API Endpoint**: Satu endpoint untuk semua services
- **Authentication & Authorization**: JWT token verification dan role-based access
- **Rate Limiting**: Perlindungan terhadap abuse dan spam
- **Request Proxying**: Intelligent routing ke service yang tepat
- **Health Monitoring**: Health check untuk semua services
- **Error Handling**: Centralized error handling dan logging
- **CORS Support**: Cross-origin resource sharing configuration
- **Security Headers**: Helmet.js untuk security headers
- **Request Compression**: Gzip compression untuk response

## 📋 Prerequisites

- Node.js >= 18.0.0
- npm atau yarn
- Services yang berjalan:
  - Auth Service (port 3001)
  - Archive Service (port 3002)
  - Assessment Service (port 3003)

## 🛠️ Installation

1. **Clone atau copy folder api-gateway**

2. **Install dependencies**
```bash
cd api-gateway
npm install
```

3. **Setup environment variables**
```bash
cp .env.example .env
```

Edit file `.env` sesuai dengan konfigurasi Anda:
```env
PORT=3000
NODE_ENV=development

AUTH_SERVICE_URL=http://localhost:3001
ARCHIVE_SERVICE_URL=http://localhost:3002
ASSESSMENT_SERVICE_URL=http://localhost:3003

JWT_SECRET=your_jwt_secret_here
INTERNAL_SERVICE_KEY=your_internal_service_key_here
```

4. **Start the gateway**
```bash
# Development mode
npm run dev

# Production mode
npm start
```

## 🌐 API Endpoints

### Base URL
```
http://localhost:3000
```

### Route Structure

| Prefix | Target Service | Description |
|--------|---------------|-------------|
| `/api/auth/*` | Auth Service | Authentication & user management |
| `/api/admin/*` | Auth Service | Admin endpoints |
| `/api/archive/*` | Archive Service | Analysis results & jobs |
| `/api/assessment/*` | Assessment Service | Assessment submission & status |

### Health Check Endpoints

- `GET /health` - Basic health check
- `GET /health/detailed` - Detailed health with all services status
- `GET /health/ready` - Readiness probe for orchestration
- `GET /health/live` - Liveness probe for orchestration

## 🔐 Authentication

### User Authentication
```bash
curl -H "Authorization: Bearer <jwt_token>" \
     http://localhost:3000/api/auth/profile
```

### Internal Service Authentication
```bash
curl -H "X-Service-Key: <service_key>" \
     -H "X-Internal-Service: true" \
     http://localhost:3000/api/auth/verify-token
```

## 📊 Rate Limiting

| Endpoint Type | Window | Max Requests |
|---------------|--------|--------------|
| General | 15 minutes | 100 requests |
| Authentication | 15 minutes | 10 requests |
| Assessment | 1 hour | 5 requests |
| Admin | 15 minutes | 200 requests |

## 🔄 Service Routing

### Auth Service Routes
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile
- `GET /api/auth/schools` - Get schools list
- `POST /api/admin/login` - Admin login

### Archive Service Routes
- `GET /api/archive/results` - Get analysis results
- `GET /api/archive/results/:id` - Get specific result
- `GET /api/archive/jobs` - Get analysis jobs
- `GET /api/archive/jobs/:jobId` - Get job status

### Assessment Service Routes
- `POST /api/assessment/submit` - Submit assessment
- `GET /api/assessment/status/:jobId` - Check assessment status

## 🛡️ Security Features

### Headers
- **Helmet.js**: Security headers (CSP, HSTS, etc.)
- **CORS**: Configurable cross-origin policies
- **Rate Limiting**: IP and user-based limiting

### Authentication
- **JWT Verification**: Token validation through auth service
- **Role-based Access**: Admin vs user permissions
- **Internal Service Keys**: Service-to-service authentication

## 📈 Monitoring

### Health Checks
```bash
# Basic health
curl http://localhost:3000/health

# Detailed health with all services
curl http://localhost:3000/health/detailed

# Readiness probe
curl http://localhost:3000/health/ready
```

### Logging
- Request/response logging dengan Morgan
- Error logging dengan stack traces (development)
- Service proxy logging

## 🚀 Deployment

### Docker (Recommended)
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY src/ ./src/
EXPOSE 3000
CMD ["npm", "start"]
```

### Environment Variables
```env
NODE_ENV=production
PORT=3000
AUTH_SERVICE_URL=http://auth-service:3001
ARCHIVE_SERVICE_URL=http://archive-service:3002
ASSESSMENT_SERVICE_URL=http://assessment-service:3003
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch
```

## 📝 Development

### Adding New Routes
1. Edit `src/routes/index.js`
2. Add authentication middleware if needed
3. Add rate limiting if appropriate
4. Update documentation

### Adding New Services
1. Add service URL to config
2. Create proxy middleware
3. Add routes in router
4. Update health checks

## 🔧 Configuration

### Rate Limiting
Edit `src/middleware/rateLimiter.js` untuk mengubah rate limiting rules.

### CORS
Edit `src/app.js` untuk mengubah CORS configuration.

### Proxy Settings
Edit `src/middleware/proxy.js` untuk mengubah proxy behavior.

## 📞 Support

Untuk pertanyaan atau issues, silakan buat issue di repository atau hubungi tim development.

## 📄 License

MIT License - see LICENSE file for details.
